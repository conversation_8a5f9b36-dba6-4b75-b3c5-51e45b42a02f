'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, Star } from 'lucide-react'

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0)

  const slides = [
    {
      id: 1,
      title: 'Exquisite Shalwar Kameez Collection',
      subtitle: 'Inspired by <PERSON><PERSON><PERSON>\'s Elegance',
      description: 'Discover our premium collection of traditional shalwar kameez featuring hand-embroidered motifs and luxurious fabrics, as worn by Pakistani celebrities.',
      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      cta: 'Shop Traditional Wear',
      link: '/traditional/shalwar-kameez',
      celebrity: '<PERSON><PERSON><PERSON>'
    },
    {
      id: 2,
      title: 'Royal Lehenga Collection',
      subtitle: '<PERSON><PERSON> Inspired Bridal Wear',
      description: 'Make every celebration unforgettable with our stunning lehengas featuring intricate zardozi work and rich Pakistani craftsmanship.',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      cta: 'Explore Lehengas',
      link: '/traditional/lehenga',
      celebrity: 'Ayeza Khan Collection'
    },
    {
      id: 3,
      title: 'Contemporary Fusion Wear',
      subtitle: 'Sajal Aly Modern Collection',
      description: 'Embrace the perfect blend of contemporary style and traditional Pakistani elegance with our celebrity-inspired fusion collection.',
      image: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      cta: 'Shop Fusion',
      link: '/fusion',
      celebrity: 'Sajal Aly Style'
    },
    {
      id: 4,
      title: 'Elegant Formal Wear',
      subtitle: 'Saba Qamar Glamour Collection',
      description: 'Step into sophistication with our formal wear collection inspired by Pakistan\'s most stylish celebrities and fashion icons.',
      image: 'https://images.unsplash.com/photo-1564584217132-2271339881b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
      cta: 'Shop Formal Wear',
      link: '/formal',
      celebrity: 'Saba Qamar Collection'
    }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [slides.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  return (
    <section className="relative h-screen overflow-hidden">
      {/* Background slides */}
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 transition-opacity duration-1000 ${
            index === currentSlide ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <Image
            src={slide.image}
            alt={slide.title}
            fill
            className="object-cover"
            priority={index === 0}
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>
      ))}

      {/* Content overlay */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="max-w-3xl">
            {slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`transition-all duration-1000 ${
                  index === currentSlide
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-8'
                }`}
              >
                {index === currentSlide && (
                  <>
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-gold-500 text-gold-500" />
                        ))}
                      </div>
                      <span className="text-white/90 text-sm">Trusted by 15,000+ customers</span>
                    </div>

                    <div className="bg-gold-500/20 backdrop-blur-sm rounded-full px-4 py-2 inline-block mb-4">
                      <span className="text-gold-300 text-sm font-medium">✨ {slide.celebrity}</span>
                    </div>

                    <h2 className="text-gold-400 text-lg font-medium mb-2 tracking-wide">
                      {slide.subtitle}
                    </h2>
                    
                    <h1 className="text-5xl md:text-7xl font-elegant font-bold text-white mb-6 leading-tight">
                      {slide.title}
                    </h1>
                    
                    <p className="text-xl text-white/90 mb-8 max-w-2xl leading-relaxed">
                      {slide.description}
                    </p>
                    
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Link
                        href={slide.link}
                        className="btn-primary inline-flex items-center justify-center text-lg px-8 py-4"
                      >
                        {slide.cta}
                      </Link>
                      <Link
                        href="/about"
                        className="border-2 border-white text-white hover:bg-white hover:text-gray-900 font-medium py-4 px-8 rounded-lg transition-all duration-200 inline-flex items-center justify-center text-lg"
                      >
                        Our Story
                      </Link>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-20 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200"
      >
        <ChevronLeft className="h-6 w-6" />
      </button>
      
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-20 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200"
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      {/* Slide indicators */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              index === currentSlide
                ? 'bg-white scale-125'
                : 'bg-white/50 hover:bg-white/75'
            }`}
          />
        ))}
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 right-8 z-20 text-white/80">
        <div className="flex flex-col items-center space-y-2">
          <span className="text-sm font-medium">Scroll</span>
          <div className="w-px h-8 bg-white/50"></div>
        </div>
      </div>
    </section>
  )
}

export default Hero
