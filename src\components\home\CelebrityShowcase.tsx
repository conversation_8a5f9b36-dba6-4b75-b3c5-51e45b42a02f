'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Star, Heart, Instagram } from 'lucide-react'

const CelebrityShowcase = () => {
  const celebrities = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      title: 'Bollywood & Lollywood Star',
      outfit: 'Royal Emerald Shalwar Kameez',
      price: '₨18,500',
      originalPrice: '₨25,000',
      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      description: 'Stunning hand-embroidered shalwar kameez with traditional Pakistani motifs',
      occasion: 'Perfect for Eid & Weddings',
      fabric: 'Premium Lawn with Chiffon Dupatta',
      instagram: '@mahirahkhan'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      title: 'Pakistani Drama Queen',
      outfit: 'Bridal Gold Lehenga',
      price: '₨95,000',
      originalPrice: '₨130,000',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      description: 'Luxurious bridal lehenga with heavy zardozi and gold thread work',
      occasion: 'Bridal & Special Occasions',
      fabric: 'Pure Silk with Gold Embroidery',
      instagram: '@ayezakhan.ak'
    },
    {
      id: 3,
      name: 'Sajal Aly',
      title: 'Fashion Icon',
      outfit: 'Contemporary Fusion Dress',
      price: '₨12,500',
      originalPrice: '₨18,000',
      image: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      description: 'Modern fusion wear blending Pakistani and western aesthetics',
      occasion: 'Parties & Formal Events',
      fabric: 'Designer Cotton with Embellishments',
      instagram: '@sajalaly'
    },
    {
      id: 4,
      name: 'Saba Qamar',
      title: 'Versatile Actress',
      outfit: 'Elegant Formal Kurta',
      price: '₨8,500',
      originalPrice: '₨12,000',
      image: 'https://images.unsplash.com/photo-1564584217132-2271339881b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      description: 'Sophisticated formal wear perfect for professional settings',
      occasion: 'Office & Business Meetings',
      fabric: 'Premium Cotton Blend',
      instagram: '@sabaqamarzaman'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-cream-50 via-white to-emerald-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-elegant font-bold text-gray-900 mb-4">
            Celebrity Inspired Collection
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover the same stunning outfits worn by Pakistan's most beloved celebrities. 
            Each piece is carefully crafted to bring you the glamour of the stars.
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-emerald-600 to-gold-500 mx-auto rounded-full"></div>
        </div>

        {/* Celebrity grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {celebrities.map((celebrity) => (
            <div
              key={celebrity.id}
              className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 hover:-translate-y-2"
            >
              <div className="grid grid-cols-1 md:grid-cols-2">
                {/* Image */}
                <div className="relative h-80 md:h-full">
                  <Image
                    src={celebrity.image}
                    alt={`${celebrity.name} wearing ${celebrity.outfit}`}
                    fill
                    className="object-cover"
                  />
                  
                  {/* Celebrity badge */}
                  <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-2 rounded-full">
                    <div className="flex items-center space-x-2">
                      <Star className="h-4 w-4 fill-gold-500 text-gold-500" />
                      <span className="text-sm font-medium">Celebrity Worn</span>
                    </div>
                  </div>

                  {/* Instagram handle */}
                  <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-2 rounded-full">
                    <div className="flex items-center space-x-2">
                      <Instagram className="h-4 w-4 text-pink-500" />
                      <span className="text-sm font-medium">{celebrity.instagram}</span>
                    </div>
                  </div>

                  {/* Wishlist button */}
                  <button className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200">
                    <Heart className="h-5 w-5 text-gray-600 hover:text-red-500" />
                  </button>
                </div>

                {/* Content */}
                <div className="p-8">
                  <div className="mb-4">
                    <h3 className="text-2xl font-elegant font-bold text-gray-900 mb-1">
                      {celebrity.name}
                    </h3>
                    <p className="text-emerald-600 font-medium">{celebrity.title}</p>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">
                      {celebrity.outfit}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed mb-3">
                      {celebrity.description}
                    </p>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-700">Occasion:</span>
                        <span className="text-gray-600">{celebrity.occasion}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-700">Fabric:</span>
                        <span className="text-gray-600">{celebrity.fabric}</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-2xl font-bold text-emerald-600">
                        {celebrity.price}
                      </span>
                      <span className="text-lg text-gray-500 line-through">
                        {celebrity.originalPrice}
                      </span>
                    </div>
                    <div className="bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                      Save ₨{parseInt(celebrity.originalPrice.replace('₨', '').replace(',', '')) - parseInt(celebrity.price.replace('₨', '').replace(',', ''))}
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="space-y-3">
                    <button className="w-full bg-gradient-to-r from-emerald-600 to-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:from-emerald-700 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                      Add to Cart
                    </button>
                    <Link
                      href={`/celebrity/${celebrity.name.toLowerCase().replace(' ', '-')}`}
                      className="w-full border-2 border-emerald-600 text-emerald-600 py-3 px-6 rounded-lg font-medium hover:bg-emerald-600 hover:text-white transition-all duration-200 text-center block"
                    >
                      View Celebrity Collection
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-emerald-600 to-gold-500 rounded-2xl p-8 text-white">
            <h3 className="text-3xl font-elegant font-bold mb-4">
              Get the Celebrity Look
            </h3>
            <p className="text-xl mb-6 opacity-90">
              Shop the complete collection and dress like your favorite Pakistani stars
            </p>
            <Link
              href="/celebrity-collection"
              className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-4 px-8 rounded-lg transition-all duration-200 inline-flex items-center space-x-2 shadow-lg hover:shadow-xl"
            >
              <span>Explore All Celebrity Looks</span>
              <Star className="h-5 w-5 fill-current" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CelebrityShowcase
