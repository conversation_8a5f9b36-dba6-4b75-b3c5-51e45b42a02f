'use client'

import { useState } from 'react'
import { Mail, Gift, Spark<PERSON>, Bell } from 'lucide-react'

const Newsletter = () => {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true)
      setIsLoading(false)
      setEmail('')
    }, 1500)
  }

  const benefits = [
    {
      icon: Gift,
      title: 'Exclusive Offers',
      description: 'Get 15% off your first order and access to member-only sales'
    },
    {
      icon: Sparkles,
      title: 'New Collections',
      description: 'Be the first to see our latest designs and seasonal collections'
    },
    {
      icon: Bell,
      title: 'Style Tips',
      description: 'Receive styling advice and cultural fashion insights'
    }
  ]

  if (isSubscribed) {
    return (
      <section className="py-20 bg-gradient-to-r from-emerald-600 to-gold-500">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="h-8 w-8 text-primary-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-elegant font-bold text-white mb-4">
              Welcome to the Zahra Family!
            </h2>
            <p className="text-xl text-white/90 mb-6">
              Thank you for subscribing! Check your email for your exclusive ₨2,000 discount code.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setIsSubscribed(false)}
                className="bg-white text-emerald-600 hover:bg-gray-100 font-medium py-3 px-6 rounded-lg transition-all duration-200"
              >
                Subscribe Another Email
              </button>
              <a
                href="/collections"
                className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 font-medium py-3 px-6 rounded-lg transition-all duration-200"
              >
                Start Shopping
              </a>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gradient-to-r from-emerald-600 to-gold-500 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 border border-white rounded-full"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 border border-white rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white rounded-full"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-white">
            <h2 className="text-4xl md:text-5xl font-elegant font-bold mb-6">
              Join Our Fashion Community
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Subscribe to our newsletter and be part of a community that celebrates Pakistani heritage 
              and contemporary fashion. Get exclusive access to new collections, styling tips, and special offers.
            </p>

            {/* Benefits */}
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <benefit.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg mb-1">{benefit.title}</h3>
                    <p className="text-white/80">{benefit.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Newsletter form */}
          <div className="bg-white rounded-2xl p-8 md:p-10 shadow-2xl">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-gold-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-elegant font-bold text-gray-900 mb-2">
                Get ₨2,000 Off Your First Order
              </h3>
              <p className="text-gray-600">
                Plus exclusive access to celebrity-inspired collections and Pakistani fashion tips
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-emerald-600 to-gold-500 text-white font-medium py-4 px-6 rounded-lg hover:from-emerald-700 hover:to-gold-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Subscribing...</span>
                  </>
                ) : (
                  <>
                    <Mail className="h-5 w-5" />
                    <span>Subscribe & Get ₨2,000 Off</span>
                  </>
                )}
              </button>

              <p className="text-xs text-gray-500 text-center">
                By subscribing, you agree to our{' '}
                <a href="/privacy" className="text-emerald-600 hover:underline">
                  Privacy Policy
                </a>{' '}
                and{' '}
                <a href="/terms" className="text-emerald-600 hover:underline">
                  Terms of Service
                </a>
                . Unsubscribe at any time.
              </p>
            </form>

            {/* Social proof */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="flex -space-x-2">
                    <div className="w-6 h-6 bg-primary-600 rounded-full border-2 border-white"></div>
                    <div className="w-6 h-6 bg-gold-500 rounded-full border-2 border-white"></div>
                    <div className="w-6 h-6 bg-terracotta-600 rounded-full border-2 border-white"></div>
                  </div>
                  <span>Join 25,000+ subscribers</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Newsletter
