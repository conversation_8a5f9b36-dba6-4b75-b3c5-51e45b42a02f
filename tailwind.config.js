/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Enhanced Pakistani cultural color palette
        primary: {
          50: '#f0f9f4',
          100: '#dcf2e3',
          200: '#bce5ca',
          300: '#8dd1a7',
          400: '#5bb77e',
          500: '#1a5f3f', // Deep Pakistani green
          600: '#164d33',
          700: '#133d29',
          800: '#0f2d1f',
          900: '#0a1d15',
        },
        gold: {
          50: '#fffdf7',
          100: '#fef9e7',
          200: '#fef2c7',
          300: '#fde68a',
          400: '#fcd34d',
          500: '#d4af37', // Rich Pakistani gold
          600: '#b8941f',
          700: '#9c7a1a',
          800: '#7d6015',
          900: '#5e4610',
        },
        rose: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#be185d', // Deep rose pink
          600: '#9d174d',
          700: '#831843',
          800: '#6b1a3a',
          900: '#4c1d2f',
        },
        emerald: {
          50: '#ecfdf5',
          100: '#d1fae5',
          200: '#a7f3d0',
          300: '#6ee7b7',
          400: '#34d399',
          500: '#047857', // Pakistani emerald
          600: '#065f46',
          700: '#064e3b',
          800: '#053e31',
          900: '#042f26',
        },
        cream: {
          50: '#fffef7',
          100: '#fffbeb',
          200: '#fef3c7',
          300: '#fde68a',
          400: '#fcd34d',
          500: '#f4e4bc', // Warm cream
          600: '#d4c5a0',
          700: '#b4a684',
          800: '#948768',
          900: '#74684c',
        }
      },
      fontFamily: {
        'urdu': ['Noto Nastaliq Urdu', 'serif'],
        'elegant': ['Playfair Display', 'serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'paisley-pattern': "url('/patterns/paisley.svg')",
      },
    },
  },
  plugins: [],
}
