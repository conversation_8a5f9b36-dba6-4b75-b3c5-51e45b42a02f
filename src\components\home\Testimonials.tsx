'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react'

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      location: 'Lahore, Pakistan',
      rating: 5,
      text: 'بہت خوبصورت! I ordered a lehenga inspired by <PERSON><PERSON><PERSON>\'s style for my cousin\'s wedding. The quality is exceptional and the embroidery work is absolutely stunning. Worth every rupee!',
      image: 'https://i.pinimg.com/736x/a1/2b/3c/a12b3c4d5e6f7a8b9c0d1e2f3a4b5c6d.jpg',
      product: '<PERSON><PERSON><PERSON> Inspired Lehenga - ₨85,000',
      verified: true
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      location: 'Karachi, Pakistan',
      rating: 5,
      text: 'I\'ve been shopping with Zahra Collections for 3 years. Their celebrity-inspired fusion wear is perfect for my office and parties. The Sajal Aly collection is my favorite!',
      image: 'https://i.pinimg.com/736x/b2/3c/4d/b23c4d5e6f7a8b9c0d1e2f3a4b5c6d7e.jpg',
      product: 'Sajal Aly Fusion Collection - ₨12,500',
      verified: true
    },
    {
      id: 3,
      name: 'Zara Ahmed',
      location: 'Islamabad, Pakistan',
      rating: 5,
      text: 'The Saba Qamar formal collection is absolutely gorgeous! Perfect for my job interviews and office wear. The fabric quality and stitching is top-notch.',
      image: 'https://i.pinimg.com/736x/c3/4d/5e/c34d5e6f7a8b9c0d1e2f3a4b5c6d7e8f.jpg',
      product: 'Saba Qamar Formal Wear - ₨8,500',
      verified: true
    },
    {
      id: 4,
      name: 'Mariam Khan',
      location: 'Faisalabad, Pakistan',
      rating: 5,
      text: 'Fast delivery across Pakistan! My Ayeza Khan inspired bridal dress arrived in perfect condition. The colors are exactly as shown and the quality exceeded my expectations.',
      image: 'https://i.pinimg.com/736x/d4/5e/6f/d45e6f7a8b9c0d1e2f3a4b5c6d7e8f9a.jpg',
      product: 'Ayeza Khan Bridal Collection - ₨95,000',
      verified: true
    },
    {
      id: 5,
      name: 'Sana Butt',
      location: 'Multan, Pakistan',
      rating: 5,
      text: 'Amazing experience! The size chart was accurate and the customer service helped me choose the perfect outfit. The celebrity-inspired designs are unique and beautiful.',
      image: 'https://i.pinimg.com/736x/e5/6f/7a/e56f7a8b9c0d1e2f3a4b5c6d7e8f9a0b.jpg',
      product: 'Designer Shalwar Kameez - ₨15,500',
      verified: true
    },
    {
      id: 6,
      name: 'Hira Siddiqui',
      location: 'Peshawar, Pakistan',
      rating: 5,
      text: 'The party wear collection is stunning! I wore the red carpet inspired dress to my friend\'s engagement and received so many compliments. Highly recommended!',
      image: 'https://i.pinimg.com/736x/f6/7a/8b/f67a8b9c0d1e2f3a4b5c6d7e8f9a0b1c.jpg',
      product: 'Red Carpet Party Wear - ₨22,000',
      verified: true
    }
  ]

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 to-cream-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-elegant font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Join thousands of satisfied customers who trust Zahra Collections for their traditional wear needs.
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-primary-600 to-gold-500 mx-auto rounded-full"></div>
        </div>

        {/* Main testimonial */}
        <div className="relative max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 relative overflow-hidden">
            {/* Quote icon */}
            <div className="absolute top-6 right-6 opacity-10">
              <Quote className="h-24 w-24 text-primary-600" />
            </div>

            {/* Content */}
            <div className="relative z-10">
              {/* Stars */}
              <div className="flex justify-center space-x-1 mb-6">
                {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                  <Star key={i} className="h-6 w-6 fill-gold-500 text-gold-500" />
                ))}
              </div>

              {/* Testimonial text */}
              <blockquote className="text-xl md:text-2xl text-gray-700 text-center leading-relaxed mb-8 font-medium">
                "{testimonials[currentTestimonial].text}"
              </blockquote>

              {/* Customer info */}
              <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Image
                      src={testimonials[currentTestimonial].image}
                      alt={testimonials[currentTestimonial].name}
                      width={60}
                      height={60}
                      className="rounded-full object-cover"
                    />
                    {testimonials[currentTestimonial].verified && (
                      <div className="absolute -bottom-1 -right-1 bg-primary-600 rounded-full p-1">
                        <svg className="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="text-center md:text-left">
                    <h4 className="font-semibold text-gray-900 text-lg">
                      {testimonials[currentTestimonial].name}
                    </h4>
                    <p className="text-gray-600">{testimonials[currentTestimonial].location}</p>
                  </div>
                </div>
                
                <div className="hidden md:block w-px h-12 bg-gray-300"></div>
                
                <div className="text-center md:text-left">
                  <p className="text-sm text-gray-500">Purchased</p>
                  <p className="font-medium text-gray-900">{testimonials[currentTestimonial].product}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white shadow-lg text-gray-600 hover:text-primary-600 hover:shadow-xl transition-all duration-200"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white shadow-lg text-gray-600 hover:text-primary-600 hover:shadow-xl transition-all duration-200"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </div>

        {/* Testimonial indicators */}
        <div className="flex justify-center space-x-3 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentTestimonial
                  ? 'bg-primary-600 scale-125'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        {/* Trust indicators */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-emerald-600 mb-2">25,000+</div>
            <div className="text-gray-600">Happy Pakistani Customers</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-emerald-600 mb-2">4.9/5</div>
            <div className="text-gray-600">Average Rating</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-emerald-600 mb-2">98%</div>
            <div className="text-gray-600">Customer Satisfaction</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-emerald-600 mb-2">200+</div>
            <div className="text-gray-600">Cities in Pakistan</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
