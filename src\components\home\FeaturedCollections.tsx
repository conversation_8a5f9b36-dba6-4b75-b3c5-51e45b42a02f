'use client'

import Image from 'next/image'
import Link from 'next/link'
import { <PERSON>R<PERSON>, Star } from 'lucide-react'

const FeaturedCollections = () => {
  const collections = [
    {
      id: 1,
      name: 'Premium Shalwar Kameez',
      description: 'Hand-embroidered shalwar kameez with traditional Pakistani motifs and premium lawn fabric',
      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨12,500',
      originalPrice: '₨15,000',
      rating: 4.9,
      reviews: 342,
      link: '/traditional',
      badge: 'Bestseller',
      celebrity: '<PERSON><PERSON><PERSON>'
    },
    {
      id: 2,
      name: 'Royal Bridal Lehenga',
      description: 'Stunning bridal lehengas with heavy zardozi work, perfect for your special day',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨85,000',
      originalPrice: '₨120,000',
      rating: 5.0,
      reviews: 156,
      link: '/bridal',
      badge: 'Premium',
      celebrity: '<PERSON><PERSON> Khan Collection'
    },
    {
      id: 3,
      name: 'Designer Fusion Wear',
      description: 'Contemporary Pakistani fashion with modern cuts and traditional embellishments',
      image: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨8,500',
      originalPrice: '₨12,000',
      rating: 4.8,
      reviews: 289,
      link: '/fusion',
      badge: 'Trending',
      celebrity: 'Sajal Aly Style'
    },
    {
      id: 4,
      name: 'Elegant Formal Wear',
      description: 'Sophisticated formal dresses perfect for office wear and evening events',
      image: 'https://images.unsplash.com/photo-1564584217132-2271339881b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨6,500',
      originalPrice: '₨9,000',
      rating: 4.7,
      reviews: 198,
      link: '/formal',
      badge: 'New Arrival',
      celebrity: 'Saba Qamar Collection'
    },
    {
      id: 5,
      name: 'Casual Kurta Collection',
      description: 'Comfortable everyday kurtas with beautiful prints and soft cotton fabric',
      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨3,500',
      originalPrice: '₨5,000',
      rating: 4.6,
      reviews: 425,
      link: '/casual',
      badge: 'Popular',
      celebrity: 'Everyday Comfort'
    },
    {
      id: 6,
      name: 'Party Wear Collection',
      description: 'Glamorous party wear with sequins and embroidery for special occasions',
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
      price: 'Starting from ₨18,500',
      originalPrice: '₨25,000',
      rating: 4.9,
      reviews: 167,
      link: '/party',
      badge: 'Exclusive',
      celebrity: 'Red Carpet Ready'
    }
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-cream-50 to-primary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-elegant font-bold text-gray-900 mb-4">
            Featured Collections
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover our carefully curated collections that celebrate the rich heritage of Pakistani fashion 
            while embracing contemporary elegance.
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-primary-600 to-gold-500 mx-auto rounded-full"></div>
        </div>

        {/* Collections grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {collections.map((collection) => (
            <div
              key={collection.id}
              className="group bg-white rounded-2xl shadow-lg overflow-hidden card-hover"
            >
              {/* Image container */}
              <div className="relative h-80 overflow-hidden">
                <Image
                  src={collection.image}
                  alt={collection.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />
                
                {/* Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    collection.badge === 'Bestseller' ? 'bg-emerald-600 text-white' :
                    collection.badge === 'Premium' ? 'bg-gold-500 text-white' :
                    collection.badge === 'Trending' ? 'bg-rose-600 text-white' :
                    collection.badge === 'New Arrival' ? 'bg-primary-600 text-white' :
                    collection.badge === 'Popular' ? 'bg-emerald-500 text-white' :
                    collection.badge === 'Exclusive' ? 'bg-gold-600 text-white' :
                    'bg-gray-800 text-white'
                  }`}>
                    {collection.badge}
                  </span>
                </div>

                {/* Celebrity tag */}
                <div className="absolute top-4 right-4">
                  <span className="bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-full text-xs">
                    ✨ {collection.celebrity}
                  </span>
                </div>

                {/* Overlay on hover */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <Link
                      href={collection.link}
                      className="w-full bg-white text-gray-900 py-3 px-4 rounded-lg font-medium text-center inline-flex items-center justify-center space-x-2 hover:bg-gray-100 transition-colors duration-200"
                    >
                      <span>View Collection</span>
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-elegant font-semibold text-gray-900 mb-2">
                  {collection.name}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {collection.description}
                </p>

                {/* Rating */}
                <div className="flex items-center space-x-2 mb-3">
                  <div className="flex space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(collection.rating)
                            ? 'fill-gold-500 text-gold-500'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {collection.rating} ({collection.reviews} reviews)
                  </span>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-emerald-600">
                      {collection.price}
                    </span>
                    {collection.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {collection.originalPrice}
                      </span>
                    )}
                  </div>
                  <Link
                    href={collection.link}
                    className="bg-gradient-to-r from-emerald-600 to-primary-600 text-white px-4 py-2 rounded-lg font-medium text-sm inline-flex items-center space-x-1 hover:from-emerald-700 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <span>Shop Now</span>
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA section */}
        <div className="text-center mt-16">
          <Link
            href="/collections"
            className="btn-primary inline-flex items-center space-x-2 text-lg px-8 py-4"
          >
            <span>View All Collections</span>
            <ArrowRight className="h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FeaturedCollections
