'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Heart, Users, Award, Sparkles } from 'lucide-react'

const BrandStory = () => {
  const stats = [
    {
      icon: Heart,
      number: '25,000+',
      label: 'Happy Customers',
      description: 'Satisfied customers in Pakistan & worldwide'
    },
    {
      icon: Users,
      number: '100+',
      label: 'Skilled Artisans',
      description: 'Master craftspeople from Lahore & Karachi'
    },
    {
      icon: Award,
      number: '20+',
      label: 'Years Experience',
      description: 'In Pakistani traditional fashion'
    },
    {
      icon: Sparkles,
      number: '1000+',
      label: 'Celebrity Inspired Designs',
      description: 'Exclusive Pakistani collections'
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="order-2 lg:order-1">
            <div className="mb-8">
              <h2 className="text-4xl md:text-5xl font-elegant font-bold text-gray-900 mb-6">
                Preserving Heritage,
                <span className="text-gradient block">Embracing Elegance</span>
              </h2>
              
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  For over a decade, Zahra Collections has been at the forefront of Pakistani fashion, 
                  celebrating the rich cultural heritage of our homeland while embracing contemporary elegance.
                </p>
                
                <p>
                  Our journey began with a simple vision: to make authentic Pakistani traditional wear 
                  accessible to women around the world, without compromising on quality or cultural authenticity.
                </p>
                
                <p>
                  Every piece in our collection tells a story - from the intricate hand-embroidered motifs 
                  that have been passed down through generations to the carefully selected fabrics that 
                  ensure both comfort and durability.
                </p>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <Link
                  href="/about"
                  className="btn-primary inline-flex items-center justify-center"
                >
                  Our Complete Story
                </Link>
                <Link
                  href="/artisans"
                  className="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 inline-flex items-center justify-center"
                >
                  Meet Our Artisans
                </Link>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="order-1 lg:order-2">
            <div className="relative">
              {/* Main image */}
              <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://i.pinimg.com/736x/4e/7a/2d/4e7a2d8f5e5c5e5c5e5c5e5c5e5c5e5c.jpg"
                  alt="Pakistani celebrity wearing traditional clothing"
                  fill
                  className="object-cover"
                />
                {/* Celebrity overlay */}
                <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-lg">
                  <p className="text-sm font-medium">Featuring Mahira Khan</p>
                  <p className="text-xs opacity-90">Pakistani Fashion Icon</p>
                </div>
              </div>

              {/* Floating card */}
              <div className="absolute -bottom-8 -left-8 bg-white p-6 rounded-xl shadow-xl border border-gray-100">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-gold-500 rounded-full flex items-center justify-center">
                    <Award className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Premium Quality</p>
                    <p className="text-sm text-gray-600">Handcrafted Excellence</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full opacity-20"></div>
              <div className="absolute top-1/2 -left-6 w-16 h-16 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full opacity-20"></div>
            </div>
          </div>
        </div>

        {/* Stats section */}
        <div className="mt-20 pt-16 border-t border-gray-200">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-600 to-gold-500 rounded-full mb-4 shadow-lg">
                  <stat.icon className="h-8 w-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-emerald-700 mb-2">{stat.number}</div>
                <div className="text-lg font-semibold text-gray-800 mb-1">{stat.label}</div>
                <div className="text-sm text-gray-600">{stat.description}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Values section */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-elegant font-bold text-gray-900 mb-4">
              Our Core Values
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do at Zahra Collections
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-xl bg-gradient-to-br from-primary-50 to-cream-50">
              <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Authenticity</h4>
              <p className="text-gray-600">
                Every design honors traditional Pakistani craftsmanship and cultural heritage.
              </p>
            </div>

            <div className="text-center p-6 rounded-xl bg-gradient-to-br from-gold-50 to-cream-50">
              <div className="w-12 h-12 bg-gold-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Quality</h4>
              <p className="text-gray-600">
                Premium fabrics and meticulous attention to detail in every stitch.
              </p>
            </div>

            <div className="text-center p-6 rounded-xl bg-gradient-to-br from-terracotta-50 to-cream-50">
              <div className="w-12 h-12 bg-terracotta-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">Community</h4>
              <p className="text-gray-600">
                Supporting local artisans and preserving traditional craftsmanship.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default BrandStory
